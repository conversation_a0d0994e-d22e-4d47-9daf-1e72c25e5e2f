{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/lib/constants.ts"], "sourcesContent": ["// Application Constants\n\n// API Configuration\nexport const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api';\nexport const API_TIMEOUT = 30000; // 30 seconds\n\n// Authentication\nexport const AUTH_TOKEN_KEY = 'auth_token';\nexport const REFRESH_TOKEN_KEY = 'refresh_token';\nexport const SESSION_STORAGE_KEY = 'user_session';\nexport const TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000; // 5 minutes before expiry\n\n// User Roles\nexport const USER_ROLES = {\n  PATIENT: 'PATIENT',\n  DENTIST: 'DENTIST',\n  ADMIN: 'ADMIN',\n} as const;\n\n// Appointment Configuration\nexport const APPOINTMENT_DURATION_OPTIONS = [\n  { value: 15, label: '15 minutes' },\n  { value: 30, label: '30 minutes' },\n  { value: 45, label: '45 minutes' },\n  { value: 60, label: '1 hour' },\n  { value: 90, label: '1.5 hours' },\n  { value: 120, label: '2 hours' },\n  { value: 180, label: '3 hours' },\n  { value: 240, label: '4 hours' },\n];\n\nexport const APPOINTMENT_TYPES = {\n  CONSULTATION: 'CONSULTATION',\n  CLEANING: 'CLEANING',\n  FILLING: 'FILLING',\n  ROOT_CANAL: 'ROOT_CANAL',\n  EXTRACTION: 'EXTRACTION',\n  CROWN: 'CROWN',\n  BRIDGE: 'BRIDGE',\n  IMPLANT: 'IMPLANT',\n  ORTHODONTICS: 'ORTHODONTICS',\n  EMERGENCY: 'EMERGENCY',\n  FOLLOW_UP: 'FOLLOW_UP',\n  CHECKUP: 'CHECKUP',\n} as const;\n\nexport const APPOINTMENT_STATUS = {\n  SCHEDULED: 'SCHEDULED',\n  CONFIRMED: 'CONFIRMED',\n  IN_PROGRESS: 'IN_PROGRESS',\n  COMPLETED: 'COMPLETED',\n  CANCELLED: 'CANCELLED',\n  NO_SHOW: 'NO_SHOW',\n  RESCHEDULED: 'RESCHEDULED',\n} as const;\n\nexport const APPOINTMENT_PRIORITY = {\n  LOW: 'LOW',\n  NORMAL: 'NORMAL',\n  HIGH: 'HIGH',\n  URGENT: 'URGENT',\n} as const;\n\n// Document Configuration\nexport const DOCUMENT_CATEGORIES = {\n  XRAY: 'XRAY',\n  PHOTO: 'PHOTO',\n  REPORT: 'REPORT',\n  PRESCRIPTION: 'PRESCRIPTION',\n  INSURANCE: 'INSURANCE',\n  CONSENT_FORM: 'CONSENT_FORM',\n  TREATMENT_PLAN: 'TREATMENT_PLAN',\n  INVOICE: 'INVOICE',\n  RECEIPT: 'RECEIPT',\n  LAB_RESULT: 'LAB_RESULT',\n  REFERRAL: 'REFERRAL',\n  OTHER: 'OTHER',\n} as const;\n\nexport const ALLOWED_FILE_TYPES = [\n  'application/pdf',\n  'image/jpeg',\n  'image/jpg',\n  'image/png',\n  'image/webp',\n  'application/dicom',\n  'text/plain',\n  'application/msword',\n  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n];\n\nexport const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB\nexport const MAX_FILES_PER_UPLOAD = 5;\n\n// UI Configuration\nexport const SIDEBAR_WIDTH = 280;\nexport const SIDEBAR_COLLAPSED_WIDTH = 80;\nexport const HEADER_HEIGHT = 64;\n\nexport const BREAKPOINTS = {\n  sm: 640,\n  md: 768,\n  lg: 1024,\n  xl: 1280,\n  '2xl': 1536,\n} as const;\n\n// Notification Configuration\nexport const NOTIFICATION_DURATION = {\n  SUCCESS: 5000,\n  ERROR: 0, // Persistent\n  WARNING: 7000,\n  INFO: 5000,\n} as const;\n\n// Date and Time Configuration\nexport const DATE_FORMATS = {\n  SHORT: 'MMM dd, yyyy',\n  LONG: 'MMMM dd, yyyy',\n  WITH_TIME: 'MMM dd, yyyy HH:mm',\n  TIME_ONLY: 'HH:mm',\n  ISO: \"yyyy-MM-dd'T'HH:mm:ss.SSSxxx\",\n} as const;\n\nexport const TIME_ZONES = [\n  { value: 'America/New_York', label: 'Eastern Time (ET)' },\n  { value: 'America/Chicago', label: 'Central Time (CT)' },\n  { value: 'America/Denver', label: 'Mountain Time (MT)' },\n  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },\n  { value: 'America/Anchorage', label: 'Alaska Time (AKT)' },\n  { value: 'Pacific/Honolulu', label: 'Hawaii Time (HT)' },\n] as const;\n\n// Working Hours Configuration\nexport const DEFAULT_WORKING_HOURS = {\n  monday: [{ start: '09:00', end: '17:00' }],\n  tuesday: [{ start: '09:00', end: '17:00' }],\n  wednesday: [{ start: '09:00', end: '17:00' }],\n  thursday: [{ start: '09:00', end: '17:00' }],\n  friday: [{ start: '09:00', end: '17:00' }],\n  saturday: [],\n  sunday: [],\n};\n\nexport const TIME_SLOTS = Array.from({ length: 48 }, (_, i) => {\n  const hour = Math.floor(i / 2);\n  const minute = i % 2 === 0 ? '00' : '30';\n  const time = `${hour.toString().padStart(2, '0')}:${minute}`;\n  const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;\n  const ampm = hour < 12 ? 'AM' : 'PM';\n  const display = `${displayHour}:${minute} ${ampm}`;\n  return { value: time, label: display };\n});\n\n// Validation Rules\nexport const VALIDATION_RULES = {\n  EMAIL: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  PHONE: /^\\+?[\\d\\s\\-\\(\\)]+$/,\n  PASSWORD_MIN_LENGTH: 8,\n  NAME_MIN_LENGTH: 2,\n  NAME_MAX_LENGTH: 50,\n  DESCRIPTION_MAX_LENGTH: 1000,\n  NOTES_MAX_LENGTH: 2000,\n} as const;\n\n// Error Messages\nexport const ERROR_MESSAGES = {\n  REQUIRED: 'This field is required',\n  INVALID_EMAIL: 'Please enter a valid email address',\n  INVALID_PHONE: 'Please enter a valid phone number',\n  PASSWORD_TOO_SHORT: `Password must be at least ${VALIDATION_RULES.PASSWORD_MIN_LENGTH} characters`,\n  NAME_TOO_SHORT: `Name must be at least ${VALIDATION_RULES.NAME_MIN_LENGTH} characters`,\n  NAME_TOO_LONG: `Name must be less than ${VALIDATION_RULES.NAME_MAX_LENGTH} characters`,\n  DESCRIPTION_TOO_LONG: `Description must be less than ${VALIDATION_RULES.DESCRIPTION_MAX_LENGTH} characters`,\n  NOTES_TOO_LONG: `Notes must be less than ${VALIDATION_RULES.NOTES_MAX_LENGTH} characters`,\n  FILE_TOO_LARGE: `File size must be less than ${MAX_FILE_SIZE / (1024 * 1024)}MB`,\n  INVALID_FILE_TYPE: 'File type not supported',\n  TOO_MANY_FILES: `Maximum ${MAX_FILES_PER_UPLOAD} files allowed`,\n  NETWORK_ERROR: 'Network error. Please check your connection and try again.',\n  UNAUTHORIZED: 'You are not authorized to perform this action',\n  FORBIDDEN: 'Access denied',\n  NOT_FOUND: 'The requested resource was not found',\n  SERVER_ERROR: 'Server error. Please try again later.',\n  VALIDATION_ERROR: 'Please check your input and try again',\n} as const;\n\n// Success Messages\nexport const SUCCESS_MESSAGES = {\n  APPOINTMENT_CREATED: 'Appointment created successfully',\n  APPOINTMENT_UPDATED: 'Appointment updated successfully',\n  APPOINTMENT_CANCELLED: 'Appointment cancelled successfully',\n  PROFILE_UPDATED: 'Profile updated successfully',\n  DOCUMENT_UPLOADED: 'Document uploaded successfully',\n  DOCUMENT_DELETED: 'Document deleted successfully',\n  PASSWORD_CHANGED: 'Password changed successfully',\n  EMAIL_SENT: 'Email sent successfully',\n  SETTINGS_SAVED: 'Settings saved successfully',\n} as const;\n\n// Feature Flags\nexport const FEATURE_FLAGS = {\n  ENABLE_NOTIFICATIONS: true,\n  ENABLE_DARK_MODE: true,\n  ENABLE_ANALYTICS: false,\n  ENABLE_CHAT: false,\n  ENABLE_VIDEO_CALLS: false,\n  ENABLE_PAYMENT_PROCESSING: false,\n  ENABLE_INSURANCE_INTEGRATION: false,\n} as const;\n\n// Environment Configuration\nexport const IS_DEVELOPMENT = process.env.NODE_ENV === 'development';\nexport const IS_PRODUCTION = process.env.NODE_ENV === 'production';\nexport const IS_TEST = process.env.NODE_ENV === 'test';\n\n// External Service URLs\nexport const EXTERNAL_URLS = {\n  PRIVACY_POLICY: '/privacy',\n  TERMS_OF_SERVICE: '/terms',\n  SUPPORT: '/support',\n  DOCUMENTATION: '/docs',\n  STATUS_PAGE: 'https://status.example.com',\n} as const;\n"], "names": [], "mappings": "AAAA,wBAAwB;AAExB,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACb,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AACxD,MAAM,cAAc,OAAO,aAAa;AAGxC,MAAM,iBAAiB;AACvB,MAAM,oBAAoB;AAC1B,MAAM,sBAAsB;AAC5B,MAAM,0BAA0B,IAAI,KAAK,MAAM,0BAA0B;AAGzE,MAAM,aAAa;IACxB,SAAS;IACT,SAAS;IACT,OAAO;AACT;AAGO,MAAM,+BAA+B;IAC1C;QAAE,OAAO;QAAI,OAAO;IAAa;IACjC;QAAE,OAAO;QAAI,OAAO;IAAa;IACjC;QAAE,OAAO;QAAI,OAAO;IAAa;IACjC;QAAE,OAAO;QAAI,OAAO;IAAS;IAC7B;QAAE,OAAO;QAAI,OAAO;IAAY;IAChC;QAAE,OAAO;QAAK,OAAO;IAAU;IAC/B;QAAE,OAAO;QAAK,OAAO;IAAU;IAC/B;QAAE,OAAO;QAAK,OAAO;IAAU;CAChC;AAEM,MAAM,oBAAoB;IAC/B,cAAc;IACd,UAAU;IACV,SAAS;IACT,YAAY;IACZ,YAAY;IACZ,OAAO;IACP,QAAQ;IACR,SAAS;IACT,cAAc;IACd,WAAW;IACX,WAAW;IACX,SAAS;AACX;AAEO,MAAM,qBAAqB;IAChC,WAAW;IACX,WAAW;IACX,aAAa;IACb,WAAW;IACX,WAAW;IACX,SAAS;IACT,aAAa;AACf;AAEO,MAAM,uBAAuB;IAClC,KAAK;IACL,QAAQ;IACR,MAAM;IACN,QAAQ;AACV;AAGO,MAAM,sBAAsB;IACjC,MAAM;IACN,OAAO;IACP,QAAQ;IACR,cAAc;IACd,WAAW;IACX,cAAc;IACd,gBAAgB;IAChB,SAAS;IACT,SAAS;IACT,YAAY;IACZ,UAAU;IACV,OAAO;AACT;AAEO,MAAM,qBAAqB;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,gBAAgB,KAAK,OAAO,MAAM,OAAO;AAC/C,MAAM,uBAAuB;AAG7B,MAAM,gBAAgB;AACtB,MAAM,0BAA0B;AAChC,MAAM,gBAAgB;AAEtB,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;AACT;AAGO,MAAM,wBAAwB;IACnC,SAAS;IACT,OAAO;IACP,SAAS;IACT,MAAM;AACR;AAGO,MAAM,eAAe;IAC1B,OAAO;IACP,MAAM;IACN,WAAW;IACX,WAAW;IACX,KAAK;AACP;AAEO,MAAM,aAAa;IACxB;QAAE,OAAO;QAAoB,OAAO;IAAoB;IACxD;QAAE,OAAO;QAAmB,OAAO;IAAoB;IACvD;QAAE,OAAO;QAAkB,OAAO;IAAqB;IACvD;QAAE,OAAO;QAAuB,OAAO;IAAoB;IAC3D;QAAE,OAAO;QAAqB,OAAO;IAAoB;IACzD;QAAE,OAAO;QAAoB,OAAO;IAAmB;CACxD;AAGM,MAAM,wBAAwB;IACnC,QAAQ;QAAC;YAAE,OAAO;YAAS,KAAK;QAAQ;KAAE;IAC1C,SAAS;QAAC;YAAE,OAAO;YAAS,KAAK;QAAQ;KAAE;IAC3C,WAAW;QAAC;YAAE,OAAO;YAAS,KAAK;QAAQ;KAAE;IAC7C,UAAU;QAAC;YAAE,OAAO;YAAS,KAAK;QAAQ;KAAE;IAC5C,QAAQ;QAAC;YAAE,OAAO;YAAS,KAAK;QAAQ;KAAE;IAC1C,UAAU,EAAE;IACZ,QAAQ,EAAE;AACZ;AAEO,MAAM,aAAa,MAAM,IAAI,CAAC;IAAE,QAAQ;AAAG,GAAG,CAAC,GAAG;IACvD,MAAM,OAAO,KAAK,KAAK,CAAC,IAAI;IAC5B,MAAM,SAAS,IAAI,MAAM,IAAI,OAAO;IACpC,MAAM,OAAO,GAAG,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,QAAQ;IAC5D,MAAM,cAAc,SAAS,IAAI,KAAK,OAAO,KAAK,OAAO,KAAK;IAC9D,MAAM,OAAO,OAAO,KAAK,OAAO;IAChC,MAAM,UAAU,GAAG,YAAY,CAAC,EAAE,OAAO,CAAC,EAAE,MAAM;IAClD,OAAO;QAAE,OAAO;QAAM,OAAO;IAAQ;AACvC;AAGO,MAAM,mBAAmB;IAC9B,OAAO;IACP,OAAO;IACP,qBAAqB;IACrB,iBAAiB;IACjB,iBAAiB;IACjB,wBAAwB;IACxB,kBAAkB;AACpB;AAGO,MAAM,iBAAiB;IAC5B,UAAU;IACV,eAAe;IACf,eAAe;IACf,oBAAoB,CAAC,0BAA0B,EAAE,iBAAiB,mBAAmB,CAAC,WAAW,CAAC;IAClG,gBAAgB,CAAC,sBAAsB,EAAE,iBAAiB,eAAe,CAAC,WAAW,CAAC;IACtF,eAAe,CAAC,uBAAuB,EAAE,iBAAiB,eAAe,CAAC,WAAW,CAAC;IACtF,sBAAsB,CAAC,8BAA8B,EAAE,iBAAiB,sBAAsB,CAAC,WAAW,CAAC;IAC3G,gBAAgB,CAAC,wBAAwB,EAAE,iBAAiB,gBAAgB,CAAC,WAAW,CAAC;IACzF,gBAAgB,CAAC,4BAA4B,EAAE,gBAAgB,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC;IAChF,mBAAmB;IACnB,gBAAgB,CAAC,QAAQ,EAAE,qBAAqB,cAAc,CAAC;IAC/D,eAAe;IACf,cAAc;IACd,WAAW;IACX,WAAW;IACX,cAAc;IACd,kBAAkB;AACpB;AAGO,MAAM,mBAAmB;IAC9B,qBAAqB;IACrB,qBAAqB;IACrB,uBAAuB;IACvB,iBAAiB;IACjB,mBAAmB;IACnB,kBAAkB;IAClB,kBAAkB;IAClB,YAAY;IACZ,gBAAgB;AAClB;AAGO,MAAM,gBAAgB;IAC3B,sBAAsB;IACtB,kBAAkB;IAClB,kBAAkB;IAClB,aAAa;IACb,oBAAoB;IACpB,2BAA2B;IAC3B,8BAA8B;AAChC;AAGO,MAAM,iBAAiB,oDAAyB;AAChD,MAAM,gBAAgB,oDAAyB;AAC/C,MAAM,UAAU,oDAAyB;AAGzC,MAAM,gBAAgB;IAC3B,gBAAgB;IAChB,kBAAkB;IAClB,SAAS;IACT,eAAe;IACf,aAAa;AACf", "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\nimport { format, parseISO, isValid, differenceInMinutes, addMinutes, startOfDay, endOfDay } from 'date-fns';\nimport { VALIDATION_RULES, ERROR_MESSAGES } from './constants';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Date and Time Utilities\nexport const dateUtils = {\n  format: (date: Date | string, formatStr: string = 'MMM dd, yyyy') => {\n    try {\n      const dateObj = typeof date === 'string' ? parseISO(date) : date;\n      return isValid(dateObj) ? format(dateObj, formatStr) : 'Invalid Date';\n    } catch {\n      return 'Invalid Date';\n    }\n  },\n\n  formatTime: (time: string) => {\n    try {\n      const [hours, minutes] = time.split(':').map(Number);\n      const date = new Date();\n      date.setHours(hours, minutes, 0, 0);\n      return format(date, 'h:mm a');\n    } catch {\n      return time;\n    }\n  },\n\n  formatDateTime: (date: Date | string) => {\n    try {\n      const dateObj = typeof date === 'string' ? parseISO(date) : date;\n      return isValid(dateObj) ? format(dateObj, 'MMM dd, yyyy h:mm a') : 'Invalid Date';\n    } catch {\n      return 'Invalid Date';\n    }\n  },\n\n  isToday: (date: Date | string) => {\n    try {\n      const dateObj = typeof date === 'string' ? parseISO(date) : date;\n      const today = new Date();\n      return dateObj.toDateString() === today.toDateString();\n    } catch {\n      return false;\n    }\n  },\n\n  isFuture: (date: Date | string) => {\n    try {\n      const dateObj = typeof date === 'string' ? parseISO(date) : date;\n      return dateObj > new Date();\n    } catch {\n      return false;\n    }\n  },\n\n  isPast: (date: Date | string) => {\n    try {\n      const dateObj = typeof date === 'string' ? parseISO(date) : date;\n      return dateObj < new Date();\n    } catch {\n      return false;\n    }\n  },\n\n  getDuration: (startTime: string, endTime: string) => {\n    try {\n      const start = new Date(`2000-01-01T${startTime}`);\n      const end = new Date(`2000-01-01T${endTime}`);\n      return differenceInMinutes(end, start);\n    } catch {\n      return 0;\n    }\n  },\n\n  addMinutesToTime: (time: string, minutes: number) => {\n    try {\n      const [hours, mins] = time.split(':').map(Number);\n      const date = new Date();\n      date.setHours(hours, mins, 0, 0);\n      const newDate = addMinutes(date, minutes);\n      return format(newDate, 'HH:mm');\n    } catch {\n      return time;\n    }\n  },\n\n  getStartOfDay: (date: Date | string) => {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    return startOfDay(dateObj);\n  },\n\n  getEndOfDay: (date: Date | string) => {\n    const dateObj = typeof date === 'string' ? parseISO(date) : date;\n    return endOfDay(dateObj);\n  },\n};\n\n// String Utilities\nexport const stringUtils = {\n  capitalize: (str: string) => {\n    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n  },\n\n  capitalizeWords: (str: string) => {\n    return str.replace(/\\w\\S*/g, (txt) =>\n      txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()\n    );\n  },\n\n  truncate: (str: string, length: number, suffix: string = '...') => {\n    if (str.length <= length) return str;\n    return str.substring(0, length) + suffix;\n  },\n\n  slugify: (str: string) => {\n    return str\n      .toLowerCase()\n      .replace(/[^\\w\\s-]/g, '')\n      .replace(/[\\s_-]+/g, '-')\n      .replace(/^-+|-+$/g, '');\n  },\n\n  initials: (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word.charAt(0))\n      .join('')\n      .toUpperCase()\n      .substring(0, 2);\n  },\n\n  formatPhoneNumber: (phone: string) => {\n    const cleaned = phone.replace(/\\D/g, '');\n    if (cleaned.length === 10) {\n      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;\n    }\n    return phone;\n  },\n\n  maskEmail: (email: string) => {\n    const [username, domain] = email.split('@');\n    if (username.length <= 2) return email;\n    const maskedUsername = username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1);\n    return `${maskedUsername}@${domain}`;\n  },\n};\n\n// Number Utilities\nexport const numberUtils = {\n  formatCurrency: (amount: number, currency: string = 'USD') => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency,\n    }).format(amount);\n  },\n\n  formatNumber: (num: number, decimals: number = 0) => {\n    return new Intl.NumberFormat('en-US', {\n      minimumFractionDigits: decimals,\n      maximumFractionDigits: decimals,\n    }).format(num);\n  },\n\n  formatFileSize: (bytes: number) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  },\n\n  clamp: (num: number, min: number, max: number) => {\n    return Math.min(Math.max(num, min), max);\n  },\n\n  randomBetween: (min: number, max: number) => {\n    return Math.floor(Math.random() * (max - min + 1)) + min;\n  },\n};\n\n// Array Utilities\nexport const arrayUtils = {\n  unique: <T>(array: T[]): T[] => {\n    return [...new Set(array)];\n  },\n\n  groupBy: <T, K extends keyof T>(array: T[], key: K): Record<string, T[]> => {\n    return array.reduce((groups, item) => {\n      const group = String(item[key]);\n      groups[group] = groups[group] || [];\n      groups[group].push(item);\n      return groups;\n    }, {} as Record<string, T[]>);\n  },\n\n  sortBy: <T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] => {\n    return [...array].sort((a, b) => {\n      const aVal = a[key];\n      const bVal = b[key];\n\n      if (aVal < bVal) return direction === 'asc' ? -1 : 1;\n      if (aVal > bVal) return direction === 'asc' ? 1 : -1;\n      return 0;\n    });\n  },\n\n  chunk: <T>(array: T[], size: number): T[][] => {\n    const chunks: T[][] = [];\n    for (let i = 0; i < array.length; i += size) {\n      chunks.push(array.slice(i, i + size));\n    }\n    return chunks;\n  },\n\n  shuffle: <T>(array: T[]): T[] => {\n    const shuffled = [...array];\n    for (let i = shuffled.length - 1; i > 0; i--) {\n      const j = Math.floor(Math.random() * (i + 1));\n      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];\n    }\n    return shuffled;\n  },\n};\n\n// Validation Utilities\nexport const validationUtils = {\n  isEmail: (email: string): boolean => {\n    return VALIDATION_RULES.EMAIL.test(email);\n  },\n\n  isPhone: (phone: string): boolean => {\n    return VALIDATION_RULES.PHONE.test(phone);\n  },\n\n  isStrongPassword: (password: string): boolean => {\n    return (\n      password.length >= VALIDATION_RULES.PASSWORD_MIN_LENGTH &&\n      /[A-Z]/.test(password) &&\n      /[a-z]/.test(password) &&\n      /\\d/.test(password) &&\n      /[!@#$%^&*(),.?\":{}|<>]/.test(password)\n    );\n  },\n\n  validateRequired: (value: any): string | null => {\n    if (value === null || value === undefined || value === '') {\n      return ERROR_MESSAGES.REQUIRED;\n    }\n    return null;\n  },\n\n  validateEmail: (email: string): string | null => {\n    if (!email) return ERROR_MESSAGES.REQUIRED;\n    if (!validationUtils.isEmail(email)) return ERROR_MESSAGES.INVALID_EMAIL;\n    return null;\n  },\n\n  validatePhone: (phone: string): string | null => {\n    if (!phone) return ERROR_MESSAGES.REQUIRED;\n    if (!validationUtils.isPhone(phone)) return ERROR_MESSAGES.INVALID_PHONE;\n    return null;\n  },\n\n  validatePassword: (password: string): string | null => {\n    if (!password) return ERROR_MESSAGES.REQUIRED;\n    if (password.length < VALIDATION_RULES.PASSWORD_MIN_LENGTH) {\n      return ERROR_MESSAGES.PASSWORD_TOO_SHORT;\n    }\n    return null;\n  },\n};\n\n// Debounce and Throttle Utilities\nexport const debounce = <T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): ((...args: Parameters<T>) => void) => {\n  let timeout: NodeJS.Timeout;\n\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n};\n\nexport const throttle = <T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): ((...args: Parameters<T>) => void) => {\n  let inThrottle: boolean;\n\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;;;;AAEA;;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,YAAY;IACvB,QAAQ,CAAC,MAAqB,YAAoB,cAAc;QAC9D,IAAI;YACF,MAAM,UAAU,OAAO,SAAS,WAAW,SAAS,QAAQ;YAC5D,OAAO,QAAQ,WAAW,OAAO,SAAS,aAAa;QACzD,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,YAAY,CAAC;QACX,IAAI;YACF,MAAM,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC;YAC7C,MAAM,OAAO,IAAI;YACjB,KAAK,QAAQ,CAAC,OAAO,SAAS,GAAG;YACjC,OAAO,OAAO,MAAM;QACtB,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,gBAAgB,CAAC;QACf,IAAI;YACF,MAAM,UAAU,OAAO,SAAS,WAAW,SAAS,QAAQ;YAC5D,OAAO,QAAQ,WAAW,OAAO,SAAS,yBAAyB;QACrE,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,SAAS,CAAC;QACR,IAAI;YACF,MAAM,UAAU,OAAO,SAAS,WAAW,SAAS,QAAQ;YAC5D,MAAM,QAAQ,IAAI;YAClB,OAAO,QAAQ,YAAY,OAAO,MAAM,YAAY;QACtD,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,UAAU,CAAC;QACT,IAAI;YACF,MAAM,UAAU,OAAO,SAAS,WAAW,SAAS,QAAQ;YAC5D,OAAO,UAAU,IAAI;QACvB,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,QAAQ,CAAC;QACP,IAAI;YACF,MAAM,UAAU,OAAO,SAAS,WAAW,SAAS,QAAQ;YAC5D,OAAO,UAAU,IAAI;QACvB,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,aAAa,CAAC,WAAmB;QAC/B,IAAI;YACF,MAAM,QAAQ,IAAI,KAAK,CAAC,WAAW,EAAE,WAAW;YAChD,MAAM,MAAM,IAAI,KAAK,CAAC,WAAW,EAAE,SAAS;YAC5C,OAAO,oBAAoB,KAAK;QAClC,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,kBAAkB,CAAC,MAAc;QAC/B,IAAI;YACF,MAAM,CAAC,OAAO,KAAK,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC;YAC1C,MAAM,OAAO,IAAI;YACjB,KAAK,QAAQ,CAAC,OAAO,MAAM,GAAG;YAC9B,MAAM,UAAU,WAAW,MAAM;YACjC,OAAO,OAAO,SAAS;QACzB,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,eAAe,CAAC;QACd,MAAM,UAAU,OAAO,SAAS,WAAW,SAAS,QAAQ;QAC5D,OAAO,WAAW;IACpB;IAEA,aAAa,CAAC;QACZ,MAAM,UAAU,OAAO,SAAS,WAAW,SAAS,QAAQ;QAC5D,OAAO,SAAS;IAClB;AACF;AAGO,MAAM,cAAc;IACzB,YAAY,CAAC;QACX,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;IAC/D;IAEA,iBAAiB,CAAC;QAChB,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,MAC5B,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,MAAM,CAAC,GAAG,WAAW;IAE3D;IAEA,UAAU,CAAC,KAAa,QAAgB,SAAiB,KAAK;QAC5D,IAAI,IAAI,MAAM,IAAI,QAAQ,OAAO;QACjC,OAAO,IAAI,SAAS,CAAC,GAAG,UAAU;IACpC;IAEA,SAAS,CAAC;QACR,OAAO,IACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;IACzB;IAEA,UAAU,CAAC;QACT,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,SAAS,CAAC,GAAG;IAClB;IAEA,mBAAmB,CAAC;QAClB,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;QACrC,IAAI,QAAQ,MAAM,KAAK,IAAI;YACzB,OAAO,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAC,GAAG,GAAG,EAAE,EAAE,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,KAAK,CAAC,IAAI;QAC9E;QACA,OAAO;IACT;IAEA,WAAW,CAAC;QACV,MAAM,CAAC,UAAU,OAAO,GAAG,MAAM,KAAK,CAAC;QACvC,IAAI,SAAS,MAAM,IAAI,GAAG,OAAO;QACjC,MAAM,iBAAiB,SAAS,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,CAAC,SAAS,MAAM,GAAG;QAChH,OAAO,GAAG,eAAe,CAAC,EAAE,QAAQ;IACtC;AACF;AAGO,MAAM,cAAc;IACzB,gBAAgB,CAAC,QAAgB,WAAmB,KAAK;QACvD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP;QACF,GAAG,MAAM,CAAC;IACZ;IAEA,cAAc,CAAC,KAAa,WAAmB,CAAC;QAC9C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,gBAAgB,CAAC;QACf,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,OAAO,CAAC,KAAa,KAAa;QAChC,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,MAAM;IACtC;IAEA,eAAe,CAAC,KAAa;QAC3B,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,MAAM,MAAM,CAAC,KAAK;IACvD;AACF;AAGO,MAAM,aAAa;IACxB,QAAQ,CAAI;QACV,OAAO;eAAI,IAAI,IAAI;SAAO;IAC5B;IAEA,SAAS,CAAuB,OAAY;QAC1C,OAAO,MAAM,MAAM,CAAC,CAAC,QAAQ;YAC3B,MAAM,QAAQ,OAAO,IAAI,CAAC,IAAI;YAC9B,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE;YACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;YACnB,OAAO;QACT,GAAG,CAAC;IACN;IAEA,QAAQ,CAAI,OAAY,KAAc,YAA4B,KAAK;QACrE,OAAO;eAAI;SAAM,CAAC,IAAI,CAAC,CAAC,GAAG;YACzB,MAAM,OAAO,CAAC,CAAC,IAAI;YACnB,MAAM,OAAO,CAAC,CAAC,IAAI;YAEnB,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,CAAC,IAAI;YACnD,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,IAAI,CAAC;YACnD,OAAO;QACT;IACF;IAEA,OAAO,CAAI,OAAY;QACrB,MAAM,SAAgB,EAAE;QACxB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,KAAM;YAC3C,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI;QACjC;QACA,OAAO;IACT;IAEA,SAAS,CAAI;QACX,MAAM,WAAW;eAAI;SAAM;QAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;YAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;YAC3C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;gBAAC,QAAQ,CAAC,EAAE;gBAAE,QAAQ,CAAC,EAAE;aAAC;QACzD;QACA,OAAO;IACT;AACF;AAGO,MAAM,kBAAkB;IAC7B,SAAS,CAAC;QACR,OAAO,uHAAA,CAAA,mBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC;IACrC;IAEA,SAAS,CAAC;QACR,OAAO,uHAAA,CAAA,mBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC;IACrC;IAEA,kBAAkB,CAAC;QACjB,OACE,SAAS,MAAM,IAAI,uHAAA,CAAA,mBAAgB,CAAC,mBAAmB,IACvD,QAAQ,IAAI,CAAC,aACb,QAAQ,IAAI,CAAC,aACb,KAAK,IAAI,CAAC,aACV,yBAAyB,IAAI,CAAC;IAElC;IAEA,kBAAkB,CAAC;QACjB,IAAI,UAAU,QAAQ,UAAU,aAAa,UAAU,IAAI;YACzD,OAAO,uHAAA,CAAA,iBAAc,CAAC,QAAQ;QAChC;QACA,OAAO;IACT;IAEA,eAAe,CAAC;QACd,IAAI,CAAC,OAAO,OAAO,uHAAA,CAAA,iBAAc,CAAC,QAAQ;QAC1C,IAAI,CAAC,gBAAgB,OAAO,CAAC,QAAQ,OAAO,uHAAA,CAAA,iBAAc,CAAC,aAAa;QACxE,OAAO;IACT;IAEA,eAAe,CAAC;QACd,IAAI,CAAC,OAAO,OAAO,uHAAA,CAAA,iBAAc,CAAC,QAAQ;QAC1C,IAAI,CAAC,gBAAgB,OAAO,CAAC,QAAQ,OAAO,uHAAA,CAAA,iBAAc,CAAC,aAAa;QACxE,OAAO;IACT;IAEA,kBAAkB,CAAC;QACjB,IAAI,CAAC,UAAU,OAAO,uHAAA,CAAA,iBAAc,CAAC,QAAQ;QAC7C,IAAI,SAAS,MAAM,GAAG,uHAAA,CAAA,mBAAgB,CAAC,mBAAmB,EAAE;YAC1D,OAAO,uHAAA,CAAA,iBAAc,CAAC,kBAAkB;QAC1C;QACA,OAAO;IACT;AACF;AAGO,MAAM,WAAW,CACtB,MACA;IAEA,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,MAAM,WAAW,CACtB,MACA;IAEA,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF", "debugId": null}}, {"offset": {"line": 594, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 651, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/app/page.tsx"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\n\nexport default function Home() {\n  return (\n    <div className=\"flex min-h-screen flex-col\">\n      {/* Hero Section */}\n      <main className=\"flex-1\">\n        <section className=\"relative overflow-hidden bg-gradient-to-br from-background via-background to-secondary/20 px-6 py-24 sm:py-32 lg:px-8\">\n          <div className=\"mx-auto max-w-7xl\">\n            <div className=\"mx-auto max-w-2xl text-center\">\n              <h1 className=\"text-4xl font-bold tracking-tight text-foreground sm:text-6xl font-poppins\">\n                Modern Dental Practice\n                <span className=\"block text-primary\">Management</span>\n              </h1>\n              <p className=\"mt-6 text-lg leading-8 text-muted-foreground\">\n                Streamline your dental practice with our secure, HIPAA-compliant platform.\n                Manage appointments, patient records, and documents with ease.\n              </p>\n              <div className=\"mt-10 flex items-center justify-center gap-x-6\">\n                <Button size=\"lg\" className=\"glass-button\">\n                  Get Started\n                </Button>\n                <Button variant=\"outline\" size=\"lg\">\n                  Learn More\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          {/* Background decoration */}\n          <div className=\"absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]\">\n            <div className=\"relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-primary to-accent-secondary opacity-20 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]\" />\n          </div>\n        </section>\n\n        {/* Features Section */}\n        <section className=\"py-24 sm:py-32\">\n          <div className=\"mx-auto max-w-7xl px-6 lg:px-8\">\n            <div className=\"mx-auto max-w-2xl text-center\">\n              <h2 className=\"text-3xl font-bold tracking-tight text-foreground sm:text-4xl font-poppins\">\n                Everything you need to manage your practice\n              </h2>\n              <p className=\"mt-6 text-lg leading-8 text-muted-foreground\">\n                Our comprehensive platform provides all the tools you need for efficient dental practice management.\n              </p>\n            </div>\n\n            <div className=\"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none\">\n              <div className=\"grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3\">\n                <Card className=\"glass-card\">\n                  <CardHeader>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      Appointment Management\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <CardDescription className=\"text-muted-foreground\">\n                      Streamlined booking system with real-time availability, automated reminders,\n                      and easy rescheduling for both patients and staff.\n                    </CardDescription>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"glass-card\">\n                  <CardHeader>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      Patient Records\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <CardDescription className=\"text-muted-foreground\">\n                      Secure, encrypted patient profiles with medical history, treatment plans,\n                      and comprehensive document management.\n                    </CardDescription>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"glass-card\">\n                  <CardHeader>\n                    <CardTitle className=\"text-xl font-semibold text-foreground\">\n                      HIPAA Compliance\n                    </CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <CardDescription className=\"text-muted-foreground\">\n                      Built-in security features, audit logging, and compliance tools\n                      to ensure your practice meets all healthcare regulations.\n                    </CardDescription>\n                  </CardContent>\n                </Card>\n              </div>\n            </div>\n          </div>\n        </section>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"border-t border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n        <div className=\"mx-auto max-w-7xl px-6 py-12 lg:px-8\">\n          <div className=\"text-center\">\n            <p className=\"text-sm leading-6 text-muted-foreground\">\n              © 2024 Dentist Appointment Management Platform. Built with security and compliance in mind.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;wBAAQ,WAAU;;0CACjB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAA6E;8DAEzF,8OAAC;oDAAK,WAAU;8DAAqB;;;;;;;;;;;;sDAEvC,8OAAC;4CAAE,WAAU;sDAA+C;;;;;;sDAI5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,WAAU;8DAAe;;;;;;8DAG3C,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;8DAAK;;;;;;;;;;;;;;;;;;;;;;;0CAQ1C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;kCAKnB,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA6E;;;;;;sDAG3F,8OAAC;4CAAE,WAAU;sDAA+C;;;;;;;;;;;;8CAK9D,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,8OAAC,gIAAA,CAAA,aAAU;kEACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAwC;;;;;;;;;;;kEAI/D,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,gIAAA,CAAA,kBAAe;4DAAC,WAAU;sEAAwB;;;;;;;;;;;;;;;;;0DAOvD,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,8OAAC,gIAAA,CAAA,aAAU;kEACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAwC;;;;;;;;;;;kEAI/D,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,gIAAA,CAAA,kBAAe;4DAAC,WAAU;sEAAwB;;;;;;;;;;;;;;;;;0DAOvD,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;;kEACd,8OAAC,gIAAA,CAAA,aAAU;kEACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAwC;;;;;;;;;;;kEAI/D,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,gIAAA,CAAA,kBAAe;4DAAC,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAajE,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnE", "debugId": null}}]}