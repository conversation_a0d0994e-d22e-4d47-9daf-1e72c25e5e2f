[{"name": "hot-reloader", "duration": 128, "timestamp": 160717556859, "id": 3, "tags": {"version": "15.3.3"}, "startTime": 1750111211522, "traceId": "04e6c9434f009b3c"}, {"name": "setup-dev-bundler", "duration": 716118, "timestamp": 160717296239, "id": 2, "parentId": 1, "tags": {}, "startTime": 1750111211262, "traceId": "04e6c9434f009b3c"}, {"name": "run-instrumentation-hook", "duration": 24, "timestamp": 160718111718, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750111212077, "traceId": "04e6c9434f009b3c"}, {"name": "start-dev-server", "duration": 1739908, "timestamp": 160716405448, "id": 1, "tags": {"cpus": "16", "platform": "win32", "memory.freeMem": "2648838144", "memory.totalMem": "16558063616", "memory.heapSizeLimit": "8328839168", "memory.rss": "187461632", "memory.heapTotal": "102154240", "memory.heapUsed": "68852080"}, "startTime": 1750111210371, "traceId": "04e6c9434f009b3c"}, {"name": "compile-path", "duration": 2634858, "timestamp": 160738098859, "id": 7, "tags": {"trigger": "/"}, "startTime": 1750111232064, "traceId": "04e6c9434f009b3c"}, {"name": "ensure-page", "duration": 2636554, "timestamp": 160738098055, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1750111232064, "traceId": "04e6c9434f009b3c"}]