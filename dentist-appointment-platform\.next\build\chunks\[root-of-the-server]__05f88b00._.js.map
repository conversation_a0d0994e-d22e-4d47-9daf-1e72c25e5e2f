{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/postcss.config.mjs"], "sourcesContent": ["const config = {\n  plugins: [\"@tailwindcss/postcss\"],\n};\n\nexport default config;\n"], "names": [], "mappings": ";;;AAAA,MAAM,SAAS;IACb,SAAS;QAAC;KAAuB;AACnC;uCAEe"}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/postcss.config.mjs/transform.ts"], "sourcesContent": ["declare const __turbopack_external_require__: (id: string, thunk: () => any, esm?: boolean) => any;\r\n\r\nimport type {Processor} from \"postcss\";\r\n\r\n// @ts-ignore\r\nimport postcss from \"@vercel/turbopack/postcss\";\r\n// @ts-ignore\r\nimport importedConfig from \"CONFIG\";\r\nimport { relative, isAbsolute, sep } from \"path\";\r\nimport type { Ipc } from \"../ipc/evaluate\";\r\nimport type { IpcInfoMessage, IpcRequestMessage } from \"./webpack-loaders\";\r\n\r\nconst contextDir = process.cwd();\r\n\r\nfunction toPath(file: string) {\r\n  const relPath = relative(contextDir, file);\r\n  if (isAbsolute(relPath)) {\r\n    throw new Error(\r\n      `Cannot depend on path (${file}) outside of root directory (${contextDir})`\r\n    );\r\n  }\r\n  return sep !== \"/\" ? relPath.replaceAll(sep, \"/\") : relPath;\r\n}\r\n\r\nlet processor: Processor | undefined;\r\n\r\nexport const init = async (ipc: Ipc<IpcInfoMessage, IpcRequestMessage>) => {\r\n  let config = importedConfig;\r\n  if (typeof config === \"function\") {\r\n    config = await config({ env: \"development\" });\r\n  }\r\n  if (typeof config === \"undefined\") {\r\n    throw new Error(\r\n      \"PostCSS config is undefined (make sure to export an function or object from config file)\"\r\n    );\r\n  }\r\n  let plugins: any[];\r\n  if (Array.isArray(config.plugins)) {\r\n    plugins = config.plugins.map((plugin: [string, any] | string | any) => {\r\n      if (Array.isArray(plugin)) {\r\n        return plugin;\r\n      } else if (typeof plugin === \"string\") {\r\n        return [plugin, {}];\r\n      } else {\r\n        return plugin;\r\n      }\r\n    });\r\n  } else if (typeof config.plugins === \"object\") {\r\n    plugins = Object.entries(config.plugins).filter(([, options]) => options);\r\n  } else {\r\n    plugins = [];\r\n  }\r\n  const loadedPlugins = plugins.map((plugin) => {\r\n    if (Array.isArray(plugin)) {\r\n      const [arg, options] = plugin;\r\n      let pluginFactory = arg;\r\n\r\n      if (typeof pluginFactory === \"string\") {\r\n        pluginFactory = require(/* turbopackIgnore: true */ pluginFactory);\r\n      }\r\n\r\n      if (pluginFactory.default) {\r\n        pluginFactory = pluginFactory.default;\r\n      }\r\n\r\n      return pluginFactory(options);\r\n    }\r\n    return plugin;\r\n  });\r\n\r\n  processor = postcss(loadedPlugins);\r\n};\r\n\r\nexport default async function transform(\r\n  ipc: Ipc<IpcInfoMessage, IpcRequestMessage>,\r\n  cssContent: string,\r\n  name: string,\r\n  sourceMap: boolean\r\n) {\r\n  const { css, map, messages } = await processor!.process(cssContent, {\r\n    from: name,\r\n    to: name,\r\n    map: sourceMap ? {\r\n      inline: false,\r\n      annotation: false,\r\n    } : undefined,\r\n  });\r\n\r\n  const assets = [];\r\n  for (const msg of messages) {\r\n    switch (msg.type) {\r\n      case \"asset\":\r\n        assets.push({\r\n          file: msg.file,\r\n          content: msg.content,\r\n          sourceMap:\r\n            !sourceMap\r\n              ? undefined\r\n              : typeof msg.sourceMap === \"string\"\r\n              ? msg.sourceMap\r\n              : JSON.stringify(msg.sourceMap),\r\n          // There is also an info field, which we currently ignore\r\n        });\r\n        break;\r\n      case \"dependency\":\r\n      case \"missing-dependency\":\r\n        ipc.sendInfo({\r\n          type: \"fileDependency\",\r\n          path: toPath(msg.file),\r\n        });\r\n        break;\r\n      case \"build-dependency\":\r\n        ipc.sendInfo({\r\n          type: \"buildDependency\",\r\n          path: toPath(msg.file),\r\n        });\r\n        break;\r\n      case \"dir-dependency\":\r\n        ipc.sendInfo({\r\n          type: \"dirDependency\",\r\n          path: toPath(msg.dir),\r\n          glob: msg.glob,\r\n        });\r\n        break;\r\n      case \"context-dependency\":\r\n        ipc.sendInfo({\r\n          type: \"dirDependency\",\r\n          path: toPath(msg.file),\r\n          glob: \"**\",\r\n        });\r\n        break;\r\n      default:\r\n        // TODO: do we need to do anything here?\r\n        break;\r\n    }\r\n  }\r\n  return {\r\n    css,\r\n    map: sourceMap ? JSON.stringify(map) : undefined,\r\n    assets,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA,aAAa;AACb;AACA,aAAa;AACb;AACA;;;;AAIA,MAAM,aAAa,QAAQ,GAAG;AAE9B,SAAS,OAAO,IAAY;IAC1B,MAAM,UAAU,CAAA,GAAA,iGAAA,CAAA,WAAQ,AAAD,EAAE,YAAY;IACrC,IAAI,CAAA,GAAA,iGAAA,CAAA,aAAU,AAAD,EAAE,UAAU;QACvB,MAAM,IAAI,MACR,CAAC,uBAAuB,EAAE,KAAK,6BAA6B,EAAE,WAAW,CAAC,CAAC;IAE/E;IACA,OAAO,iGAAA,CAAA,MAAG,KAAK,MAAM,QAAQ,UAAU,CAAC,iGAAA,CAAA,MAAG,EAAE,OAAO;AACtD;AAEA,IAAI;AAEG,MAAM,OAAO,OAAO;IACzB,IAAI,SAAS,+GAAA,CAAA,UAAc;IAC3B,IAAI,OAAO,WAAW,YAAY;QAChC,SAAS,MAAM,OAAO;YAAE,KAAK;QAAc;IAC7C;IACA,IAAI,OAAO,WAAW,aAAa;QACjC,MAAM,IAAI,MACR;IAEJ;IACA,IAAI;IACJ,IAAI,MAAM,OAAO,CAAC,OAAO,OAAO,GAAG;QACjC,UAAU,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAI,MAAM,OAAO,CAAC,SAAS;gBACzB,OAAO;YACT,OAAO,IAAI,OAAO,WAAW,UAAU;gBACrC,OAAO;oBAAC;oBAAQ,CAAC;iBAAE;YACrB,OAAO;gBACL,OAAO;YACT;QACF;IACF,OAAO,IAAI,OAAO,OAAO,OAAO,KAAK,UAAU;QAC7C,UAAU,OAAO,OAAO,CAAC,OAAO,OAAO,EAAE,MAAM,CAAC,CAAC,GAAG,QAAQ,GAAK;IACnE,OAAO;QACL,UAAU,EAAE;IACd;IACA,MAAM,gBAAgB,QAAQ,GAAG,CAAC,CAAC;QACjC,IAAI,MAAM,OAAO,CAAC,SAAS;YACzB,MAAM,CAAC,KAAK,QAAQ,GAAG;YACvB,IAAI,gBAAgB;YAEpB,IAAI,OAAO,kBAAkB,UAAU;gBACrC,gBAAgB,QAAQ,yBAAyB,GAAG;YACtD;YAEA,IAAI,cAAc,OAAO,EAAE;gBACzB,gBAAgB,cAAc,OAAO;YACvC;YAEA,OAAO,cAAc;QACvB;QACA,OAAO;IACT;IAEA,YAAY,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE;AACtB;AAEe,eAAe,UAC5B,GAA2C,EAC3C,UAAkB,EAClB,IAAY,EACZ,SAAkB;IAElB,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,MAAM,UAAW,OAAO,CAAC,YAAY;QAClE,MAAM;QACN,IAAI;QACJ,KAAK,YAAY;YACf,QAAQ;YACR,YAAY;QACd,IAAI;IACN;IAEA,MAAM,SAAS,EAAE;IACjB,KAAK,MAAM,OAAO,SAAU;QAC1B,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,OAAO,IAAI,CAAC;oBACV,MAAM,IAAI,IAAI;oBACd,SAAS,IAAI,OAAO;oBACpB,WACE,CAAC,YACG,YACA,OAAO,IAAI,SAAS,KAAK,WACzB,IAAI,SAAS,GACb,KAAK,SAAS,CAAC,IAAI,SAAS;gBAEpC;gBACA;YACF,KAAK;YACL,KAAK;gBACH,IAAI,QAAQ,CAAC;oBACX,MAAM;oBACN,MAAM,OAAO,IAAI,IAAI;gBACvB;gBACA;YACF,KAAK;gBACH,IAAI,QAAQ,CAAC;oBACX,MAAM;oBACN,MAAM,OAAO,IAAI,IAAI;gBACvB;gBACA;YACF,KAAK;gBACH,IAAI,QAAQ,CAAC;oBACX,MAAM;oBACN,MAAM,OAAO,IAAI,GAAG;oBACpB,MAAM,IAAI,IAAI;gBAChB;gBACA;YACF,KAAK;gBACH,IAAI,QAAQ,CAAC;oBACX,MAAM;oBACN,MAAM,OAAO,IAAI,IAAI;oBACrB,MAAM;gBACR;gBACA;YACF;gBAEE;QACJ;IACF;IACA,OAAO;QACL;QACA,KAAK,YAAY,KAAK,SAAS,CAAC,OAAO;QACvC;IACF;AACF"}}]}